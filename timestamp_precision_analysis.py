#!/usr/bin/env python3
"""
精确分析时间戳偏差问题的诊断工具
"""

import os
import sys
import json
import librosa
import numpy as np
from funasr import AutoModel
import traceback
import re

class TimestampPrecisionAnalyzer:
    def __init__(self, model_dir="iic/SenseVoiceSmall"):
        """初始化精确分析器"""
        self.model_dir = model_dir
        self.model = None
        
    def load_model(self):
        """加载FunASR模型"""
        try:
            self.model = AutoModel(
                model=self.model_dir,
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000},
                device="cuda:1"
            )
            print("✓ 模型加载成功")
            return True
        except Exception as e:
            print(f"✗ 模型加载失败: {str(e)}")
            return False
    
    def analyze_audio_with_vad(self, audio_path):
        """使用VAD分析音频的实际语音段落"""
        try:
            print(f"\n=== VAD语音活动检测分析 ===")
            
            # 使用librosa加载音频
            y, sr = librosa.load(audio_path, sr=16000)  # FunASR通常使用16kHz
            duration = len(y) / sr
            
            print(f"音频采样率: {sr} Hz")
            print(f"音频总时长: {duration:.3f}秒")
            
            # 使用FunASR的VAD进行语音活动检测
            vad_result = self.model.generate(
                input=audio_path,
                cache={},
                language="auto",
                use_itn=False,
                batch_size_s=60,
                merge_vad=False,  # 不合并VAD段落，获取原始VAD信息
                merge_length_s=0,
                output_timestamp=True
            )
            
            if vad_result and len(vad_result) > 0:
                print(f"VAD检测到 {len(vad_result)} 个语音段落")
                
                # 分析最后几个语音段落
                for i, segment in enumerate(vad_result[-3:]):
                    idx = len(vad_result) - 3 + i
                    if "timestamp" in segment:
                        timestamps = segment["timestamp"]
                        if timestamps:
                            first_ts = timestamps[0]
                            last_ts = timestamps[-1]
                            start_time = first_ts[0] * 0.001007  # 使用当前转换系数
                            end_time = last_ts[1] * 0.001007
                            
                            print(f"段落 {idx+1}: {start_time:.3f}s - {end_time:.3f}s")
                            print(f"  文本: {segment.get('text', '')[:50]}...")
                            print(f"  时间戳数量: {len(timestamps)}")
            
            return vad_result, duration, sr
            
        except Exception as e:
            print(f"✗ VAD分析失败: {str(e)}")
            traceback.print_exc()
            return None, 0, 0
    
    def analyze_frame_rate_accuracy(self, audio_path):
        """分析帧率转换系数的准确性"""
        try:
            print(f"\n=== 帧率转换系数精确分析 ===")
            
            # 获取音频基本信息
            y, sr = librosa.load(audio_path, sr=None)
            original_duration = len(y) / sr
            
            # 重新采样到16kHz（FunASR标准）
            y_16k = librosa.resample(y, orig_sr=sr, target_sr=16000)
            duration_16k = len(y_16k) / 16000
            
            print(f"原始采样率: {sr} Hz, 时长: {original_duration:.6f}s")
            print(f"16kHz采样率: 16000 Hz, 时长: {duration_16k:.6f}s")
            
            # 使用FunASR转录获取时间戳
            result = self.model.generate(
                input=audio_path,
                cache={},
                language="auto",
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
                output_timestamp=True
            )
            
            if result and len(result) > 0 and "timestamp" in result[0]:
                timestamps = result[0]["timestamp"]
                max_frame = timestamps[-1][1]
                
                print(f"最大帧数: {max_frame}")
                
                # 测试不同的转换系数
                conversion_ratios = [
                    ("当前使用", duration_16k / max_frame),
                    ("原始音频基准", original_duration / max_frame),
                    ("理论10ms", 0.01),
                    ("理论20ms", 0.02),
                    ("SenseVoice标准", 0.001),
                ]
                
                print(f"\n转换系数对比分析:")
                for name, ratio in conversion_ratios:
                    converted_duration = max_frame * ratio
                    error_16k = abs(converted_duration - duration_16k)
                    error_orig = abs(converted_duration - original_duration)
                    
                    print(f"{name:12s}: {ratio:.6f} -> {converted_duration:.3f}s")
                    print(f"             误差(16kHz): {error_16k:.3f}s, 误差(原始): {error_orig:.3f}s")
                
                return timestamps, duration_16k, original_duration
            
        except Exception as e:
            print(f"✗ 帧率分析失败: {str(e)}")
            traceback.print_exc()
            return None, 0, 0
    
    def analyze_text_segmentation_accuracy(self, full_text, timestamps):
        """分析文本分段的准确性"""
        try:
            print(f"\n=== 文本分段准确性分析 ===")
            
            # 当前的分段逻辑
            text_segments = re.split(r'[。，？！.?!,;；]', full_text)
            text_segments = [seg.strip() for seg in text_segments if seg.strip()]
            
            print(f"原始文本长度: {len(full_text)}")
            print(f"分段数量: {len(text_segments)}")
            print(f"时间戳数量: {len(timestamps)}")
            print(f"平均每段词数: {len(timestamps) / len(text_segments):.2f}")
            
            # 分析最后几个分段
            print(f"\n最后5个文本分段:")
            for i, segment in enumerate(text_segments[-5:]):
                idx = len(text_segments) - 5 + i
                print(f"段落 {idx+1}: {segment}")
            
            # 分析字符分布
            total_chars = sum(len(seg) for seg in text_segments)
            print(f"\n字符分布分析:")
            print(f"总字符数: {total_chars}")
            print(f"平均每段字符数: {total_chars / len(text_segments):.1f}")
            
            # 分析最后一段的详细信息
            last_segment = text_segments[-1]
            last_segment_chars = len(last_segment)
            expected_words = int(len(timestamps) * last_segment_chars / total_chars)
            
            print(f"\n最后一段详细分析:")
            print(f"文本: '{last_segment}'")
            print(f"字符数: {last_segment_chars}")
            print(f"预期词数: {expected_words}")
            
            return text_segments
            
        except Exception as e:
            print(f"✗ 文本分段分析失败: {str(e)}")
            return []
    
    def analyze_cumulative_error(self, timestamps, text_segments, conversion_ratio):
        """分析累积误差"""
        try:
            print(f"\n=== 累积误差分析 ===")
            
            # 模拟当前的映射算法
            words_per_sentence = len(timestamps) / len(text_segments)
            word_index = 0
            cumulative_errors = []
            
            for i, segment in enumerate(text_segments):
                # 计算预期的词范围
                if i == len(text_segments) - 1:
                    # 最后一句
                    expected_start = word_index
                    expected_end = len(timestamps)
                else:
                    # 根据字符比例分配
                    segment_length = len(segment)
                    total_remaining_length = sum(len(s) for s in text_segments[i:])
                    remaining_words = len(timestamps) - word_index
                    
                    words_for_this_sentence = max(1, int(remaining_words * segment_length / total_remaining_length))
                    expected_start = word_index
                    expected_end = min(word_index + words_for_this_sentence, len(timestamps))
                
                # 计算时间
                start_time = timestamps[expected_start][0] * conversion_ratio
                end_time = timestamps[min(expected_end - 1, len(timestamps) - 1)][1] * conversion_ratio
                
                # 计算理想时间（均匀分布）
                ideal_start = (i / len(text_segments)) * (timestamps[-1][1] * conversion_ratio)
                ideal_end = ((i + 1) / len(text_segments)) * (timestamps[-1][1] * conversion_ratio)
                
                # 计算误差
                start_error = abs(start_time - ideal_start)
                end_error = abs(end_time - ideal_end)
                
                cumulative_errors.append({
                    'segment_idx': i,
                    'text': segment[:30] + '...' if len(segment) > 30 else segment,
                    'word_range': f"[{expected_start}-{expected_end-1}]",
                    'actual_time': f"{start_time:.3f}-{end_time:.3f}",
                    'ideal_time': f"{ideal_start:.3f}-{ideal_end:.3f}",
                    'start_error': start_error,
                    'end_error': end_error
                })
                
                word_index = expected_end
            
            # 显示最后几个段落的误差
            print(f"最后5个段落的时间分配误差:")
            for error_info in cumulative_errors[-5:]:
                print(f"段落 {error_info['segment_idx']+1}:")
                print(f"  文本: {error_info['text']}")
                print(f"  词范围: {error_info['word_range']}")
                print(f"  实际时间: {error_info['actual_time']}")
                print(f"  理想时间: {error_info['ideal_time']}")
                print(f"  开始误差: {error_info['start_error']:.3f}s")
                print(f"  结束误差: {error_info['end_error']:.3f}s")
                print()
            
            return cumulative_errors
            
        except Exception as e:
            print(f"✗ 累积误差分析失败: {str(e)}")
            return []

def main():
    """主函数"""
    audio_path = "test.mp3"
    
    if not os.path.exists(audio_path):
        print(f"✗ 音频文件不存在: {audio_path}")
        return
    
    analyzer = TimestampPrecisionAnalyzer()
    
    # 1. 加载模型
    if not analyzer.load_model():
        return
    
    # 2. VAD分析
    vad_result, duration, sr = analyzer.analyze_audio_with_vad(audio_path)
    
    # 3. 帧率转换系数分析
    timestamps, duration_16k, original_duration = analyzer.analyze_frame_rate_accuracy(audio_path)
    
    if timestamps:
        # 4. 获取完整转录文本
        result = analyzer.model.generate(
            input=audio_path,
            cache={},
            language="auto",
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15,
            output_timestamp=True
        )
        
        if result and len(result) > 0:
            full_text = result[0]["text"]
            
            # 5. 文本分段分析
            text_segments = analyzer.analyze_text_segmentation_accuracy(full_text, timestamps)
            
            # 6. 累积误差分析
            conversion_ratio = duration_16k / timestamps[-1][1]
            cumulative_errors = analyzer.analyze_cumulative_error(timestamps, text_segments, conversion_ratio)
            
            # 7. 总结分析结果
            print(f"\n=== 问题诊断总结 ===")
            if cumulative_errors:
                last_error = cumulative_errors[-1]
                print(f"最后一句话的时间戳偏差:")
                print(f"  开始时间误差: {last_error['start_error']:.3f}秒")
                print(f"  结束时间误差: {last_error['end_error']:.3f}秒")
                
                # 分析偏差原因
                avg_start_error = sum(e['start_error'] for e in cumulative_errors) / len(cumulative_errors)
                avg_end_error = sum(e['end_error'] for e in cumulative_errors) / len(cumulative_errors)
                
                print(f"  平均开始时间误差: {avg_start_error:.3f}秒")
                print(f"  平均结束时间误差: {avg_end_error:.3f}秒")
                
                if last_error['start_error'] > avg_start_error * 2:
                    print("⚠️ 最后一句的开始时间误差明显高于平均值，可能存在累积误差")
                
                if last_error['end_error'] > avg_end_error * 2:
                    print("⚠️ 最后一句的结束时间误差明显高于平均值，可能存在时间戳分配问题")

if __name__ == "__main__":
    main()
