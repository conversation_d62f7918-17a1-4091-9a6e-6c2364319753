nohup: ignoring input
2025-08-20 09:08:15,071 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 09:08:15,079 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 09:08:15,132 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 09:08:15,165 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 09:08:29,903 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 09:08:29,929 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 09:08:30,068 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
2025-08-20 09:08:30,077 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
funasr version: 1.2.6.
Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
New version is available: 1.2.7.
Please use the command "pip install -U funasr" to upgrade.
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/SenseVoiceSmall
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model to directory: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
初始化翻译服务，连接到: http://**************:5006
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [3901622]
INFO:     Started server process [3901622]
INFO:     Waiting for application startup.
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10093 (Press CTRL+C to quit)
INFO:     Uvicorn running on http://0.0.0.0:10097 (Press CTRL+C to quit)
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [3901622]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10094 (Press CTRL+C to quit)
初始化翻译服务，连接到: http://**************:5006
INFO:     Started server process [3901622]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10098 (Press CTRL+C to quit)
