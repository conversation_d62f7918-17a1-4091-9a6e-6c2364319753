#!/usr/bin/env python3
"""
时间戳处理逻辑分析和诊断工具
用于分析 funasr_api_zm.py 中的时间戳计算问题
"""

import os
import json
import librosa
import numpy as np
from funasr import AutoModel
import traceback

class TimestampAnalyzer:
    def __init__(self, model_dir="iic/SenseVoiceSmall"):
        """初始化分析器"""
        self.model_dir = model_dir
        self.model = None
        
    def load_model(self):
        """加载FunASR模型"""
        try:
            self.model = AutoModel(
                model=self.model_dir,
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000},
                device="cuda:1"
            )
            print("✓ 模型加载成功")
            return True
        except Exception as e:
            print(f"✗ 模型加载失败: {str(e)}")
            return False
    
    def analyze_audio_properties(self, audio_path):
        """分析音频文件的基本属性"""
        try:
            # 使用librosa分析音频
            y, sr = librosa.load(audio_path, sr=None)
            duration = len(y) / sr
            
            print(f"\n=== 音频文件属性分析 ===")
            print(f"文件路径: {audio_path}")
            print(f"采样率: {sr} Hz")
            print(f"音频长度: {len(y)} 采样点")
            print(f"时长: {duration:.3f} 秒")
            print(f"时长 (分:秒): {int(duration//60)}:{int(duration%60):02d}")
            
            return {
                "sample_rate": sr,
                "duration_seconds": duration,
                "total_samples": len(y)
            }
        except Exception as e:
            print(f"✗ 音频分析失败: {str(e)}")
            return None
    
    def test_funasr_transcription(self, audio_path):
        """测试FunASR转录并分析时间戳"""
        if not self.model:
            print("✗ 模型未加载")
            return None
            
        try:
            print(f"\n=== FunASR转录测试 ===")
            print("开始转录...")
            
            result = self.model.generate(
                input=audio_path,
                cache={},
                language="auto",
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
                output_timestamp=True
            )
            
            if not result or len(result) == 0:
                print("✗ 转录结果为空")
                return None
                
            transcription_data = result[0]
            print(f"✓ 转录完成")
            print(f"文本: {transcription_data.get('text', '')[:100]}...")
            
            # 分析时间戳
            if "timestamp" in transcription_data:
                timestamps = transcription_data["timestamp"]
                print(f"\n=== 时间戳分析 ===")
                print(f"时间戳数量: {len(timestamps)}")
                
                if len(timestamps) > 0:
                    print(f"第一个时间戳: {timestamps[0]}")
                    print(f"最后一个时间戳: {timestamps[-1]}")
                    
                    # 分析时间戳格式
                    first_ts = timestamps[0]
                    if len(first_ts) == 2:
                        start_frame, end_frame = first_ts
                        print(f"时间戳格式: [start_frame, end_frame]")
                        print(f"帧范围: {start_frame} - {end_frame}")
                        
                        # 使用当前代码的转换逻辑
                        start_time_current = start_frame * 0.01
                        end_time_current = end_frame * 0.01
                        print(f"当前转换逻辑 (×0.01): {start_time_current:.3f}s - {end_time_current:.3f}s")
                        
                        # 分析最后一个时间戳
                        last_ts = timestamps[-1]
                        last_start_frame, last_end_frame = last_ts
                        last_end_time_current = last_end_frame * 0.01
                        print(f"最后时间戳转换: {last_end_time_current:.3f}s")
                        print(f"最后时间戳 (分:秒): {int(last_end_time_current//60)}:{int(last_end_time_current%60):02d}")
                        
                        return {
                            "timestamps": timestamps,
                            "timestamp_count": len(timestamps),
                            "first_timestamp": first_ts,
                            "last_timestamp": last_ts,
                            "converted_duration": last_end_time_current,
                            "text": transcription_data.get('text', '')
                        }
                else:
                    print("✗ 时间戳列表为空")
            else:
                print("✗ 转录结果中没有时间戳信息")
                
            return transcription_data
            
        except Exception as e:
            print(f"✗ 转录失败: {str(e)}")
            traceback.print_exc()
            return None
    
    def test_different_conversion_rates(self, timestamps, actual_duration):
        """测试不同的帧率转换系数"""
        if not timestamps:
            return
            
        print(f"\n=== 帧率转换系数测试 ===")
        print(f"实际音频时长: {actual_duration:.3f}s")
        
        last_frame = timestamps[-1][1]  # 最后一个时间戳的结束帧
        
        # 测试不同的转换系数
        conversion_rates = [0.01, 0.02, 0.025, 0.04, 0.05, 0.1]
        
        for rate in conversion_rates:
            converted_duration = last_frame * rate
            error = abs(converted_duration - actual_duration)
            error_percent = (error / actual_duration) * 100
            
            print(f"转换系数 {rate:5.3f}: {converted_duration:8.3f}s, "
                  f"误差: {error:6.3f}s ({error_percent:5.1f}%)")
        
        # 计算最佳转换系数
        optimal_rate = actual_duration / last_frame
        print(f"\n最佳转换系数: {optimal_rate:.6f}")
        print(f"使用最佳系数的时长: {last_frame * optimal_rate:.3f}s")
    
    def analyze_srt_format(self, timestamps, conversion_rate=0.01):
        """分析SRT格式转换"""
        print(f"\n=== SRT格式转换分析 ===")
        
        def format_time_to_srt(seconds):
            """将秒转换为 SRT 格式时间"""
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            millis = int((seconds % 1) * 1000)
            return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"
        
        print("前5个时间戳的SRT格式:")
        for i, (start_frame, end_frame) in enumerate(timestamps[:5]):
            start_time = start_frame * conversion_rate
            end_time = end_frame * conversion_rate
            start_srt = format_time_to_srt(start_time)
            end_srt = format_time_to_srt(end_time)
            print(f"{i+1:2d}: {start_srt} --> {end_srt}")
        
        if len(timestamps) > 5:
            print("...")
            # 显示最后一个时间戳
            last_idx = len(timestamps) - 1
            start_frame, end_frame = timestamps[last_idx]
            start_time = start_frame * conversion_rate
            end_time = end_frame * conversion_rate
            start_srt = format_time_to_srt(start_time)
            end_srt = format_time_to_srt(end_time)
            print(f"{last_idx+1:2d}: {start_srt} --> {end_srt}")

def main():
    """主函数"""
    audio_path = "test.mp3"
    
    if not os.path.exists(audio_path):
        print(f"✗ 音频文件不存在: {audio_path}")
        return
    
    analyzer = TimestampAnalyzer()
    
    # 1. 分析音频属性
    audio_props = analyzer.analyze_audio_properties(audio_path)
    if not audio_props:
        return
    
    # 2. 加载模型并转录
    if not analyzer.load_model():
        return
    
    transcription_result = analyzer.test_funasr_transcription(audio_path)
    if not transcription_result:
        return
    
    # 3. 测试不同转换系数
    if "timestamps" in transcription_result:
        analyzer.test_different_conversion_rates(
            transcription_result["timestamps"], 
            audio_props["duration_seconds"]
        )
        
        # 4. 分析SRT格式
        analyzer.analyze_srt_format(transcription_result["timestamps"])
        
        # 5. 计算最佳转换系数并重新分析
        last_frame = transcription_result["timestamps"][-1][1]
        optimal_rate = audio_props["duration_seconds"] / last_frame
        print(f"\n=== 使用最佳转换系数重新分析 ===")
        analyzer.analyze_srt_format(transcription_result["timestamps"], optimal_rate)

if __name__ == "__main__":
    main()
