#!/usr/bin/env python3
"""
测试修复后的时间戳映射逻辑
"""

import os
import sys
import json

def test_fixed_timestamp_mapping():
    """测试修复后的时间戳映射"""
    
    print("=== 测试修复后的时间戳映射逻辑 ===")
    
    try:
        # 导入修复后的类
        sys.path.append('/data/funasr')
        from funasr_api_zm import FunASRTranscriber
        
        # 创建转录器实例
        transcriber = FunASRTranscriber("iic/SenseVoiceSmall", "cuda:1")
        
        # 转录测试文件
        audio_path = "test.mp3"
        print(f"转录文件: {audio_path}")
        
        text, result, audio_duration = transcriber.transcribe(audio_path)
        print(f"✓ 转录完成")
        print(f"音频时长: {audio_duration:.3f}秒 ({int(audio_duration//60)}:{int(audio_duration%60):02d})")
        print(f"文本长度: {len(text)}")
        
        # 生成SRT文件测试
        if result and "timestamp" in result[0]:
            timestamps = result[0]["timestamp"]
            print(f"词级时间戳数量: {len(timestamps)}")
            
            # 显示前几个和后几个时间戳
            print(f"前5个时间戳: {timestamps[:5]}")
            print(f"后5个时间戳: {timestamps[-5:]}")
            
            # 计算转换系数
            frame_to_second_ratio = audio_duration / timestamps[-1][1]
            print(f"帧率转换系数: {frame_to_second_ratio:.6f}")
            
            # 验证最后一个时间戳
            last_timestamp_seconds = timestamps[-1][1] * frame_to_second_ratio
            print(f"最后一个词的时间戳: {last_timestamp_seconds:.3f}秒 ({int(last_timestamp_seconds//60)}:{int(last_timestamp_seconds%60):02d})")
            
            # 生成SRT文件
            srt_base_path = "/tmp/test_fixed_v2"
            srt_paths = transcriber.generate_bilingual_srt(
                text,
                srt_base_path,
                transcription_result=result,
                target_languages=['en'],
                audio_duration=audio_duration
            )
            
            print(f"✓ SRT文件生成完成: {list(srt_paths.keys())}")
            
            # 分析生成的SRT文件
            for lang, srt_path in srt_paths.items():
                if os.path.exists(srt_path):
                    analyze_srt_timestamps(srt_path, audio_duration)
                else:
                    print(f"✗ {lang} SRT文件不存在: {srt_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_srt_timestamps(srt_path, expected_duration):
    """分析SRT文件的时间戳准确性"""
    try:
        print(f"\n=== 分析SRT文件: {srt_path} ===")
        
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取时间戳
        import re
        timestamp_pattern = r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})'
        timestamps = re.findall(timestamp_pattern, content)
        
        if not timestamps:
            print("✗ 无法提取时间戳")
            return
        
        print(f"字幕块数量: {len(timestamps)}")
        
        # 转换时间戳为秒
        def time_to_seconds(time_str):
            h, m, s_ms = time_str.split(':')
            s, ms = s_ms.split(',')
            return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000
        
        # 分析第一个和最后一个时间戳
        first_start = time_to_seconds(timestamps[0][0])
        first_end = time_to_seconds(timestamps[0][1])
        last_start = time_to_seconds(timestamps[-1][0])
        last_end = time_to_seconds(timestamps[-1][1])
        
        print(f"第一个字幕: {timestamps[0][0]} --> {timestamps[0][1]}")
        print(f"最后一个字幕: {timestamps[-1][0]} --> {timestamps[-1][1]}")
        print(f"SRT总时长: {last_end:.3f}秒 ({int(last_end//60)}:{int(last_end%60):02d})")
        print(f"预期音频时长: {expected_duration:.3f}秒 ({int(expected_duration//60)}:{int(expected_duration%60):02d})")
        
        # 计算误差
        time_error = abs(last_end - expected_duration)
        error_percentage = (time_error / expected_duration) * 100
        
        print(f"时间误差: {time_error:.3f}秒 ({error_percentage:.2f}%)")
        
        # 判断修复效果
        if time_error < 5.0:  # 误差小于5秒认为是成功的
            print("✅ 时间戳修复成功！")
        elif time_error < 30.0:  # 误差小于30秒认为是可接受的
            print("⚠️ 时间戳基本正确，但仍有小幅偏差")
        else:
            print("❌ 时间戳仍然存在较大偏差")
        
        # 显示最后几个字幕块的内容
        print(f"\n最后3个字幕块:")
        lines = content.strip().split('\n\n')
        for i, block in enumerate(lines[-3:]):
            if block.strip():
                print(f"--- 字幕块 {len(lines)-2+i} ---")
                for line in block.split('\n'):
                    print(f"  {line}")
        
        return last_end
        
    except Exception as e:
        print(f"✗ 分析SRT文件失败: {str(e)}")
        return 0

def compare_with_original_method():
    """对比原始方法和修复后方法的效果"""
    print(f"\n=== 对比修复前后的效果 ===")
    
    # 这里可以添加对比逻辑
    print("原始方法 (0.01转换系数):")
    print("- 最后时间戳: 02:06:07,600 (7567.6秒)")
    print("- 误差: 6805.552秒 (893.1%)")
    print()
    print("修复后方法:")
    print("- 使用词级时间戳映射到句子级")
    print("- 动态计算转换系数")
    print("- 正确分配时间戳范围")

if __name__ == "__main__":
    success = test_fixed_timestamp_mapping()
    if success:
        compare_with_original_method()
    else:
        print("测试失败，无法进行对比")
