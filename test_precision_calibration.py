#!/usr/bin/env python3
"""
测试精确校准后的时间戳效果
"""

import os
import sys
import json
import re

def test_precision_calibration():
    """测试精确校准后的时间戳"""
    
    print("=== 测试精确校准后的时间戳映射 ===")
    
    try:
        # 导入修复后的类
        sys.path.append('/data/funasr')
        from funasr_api_zm import FunASRTranscriber
        
        # 创建转录器实例
        transcriber = FunASRTranscriber("iic/SenseVoiceSmall", "cuda:1")
        
        # 转录测试文件
        audio_path = "test.mp3"
        print(f"转录文件: {audio_path}")
        
        text, result, audio_duration = transcriber.transcribe(audio_path)
        print(f"✓ 转录完成")
        print(f"音频时长: {audio_duration:.3f}秒 ({int(audio_duration//60)}:{int(audio_duration%60):02d})")
        print(f"文本长度: {len(text)}")
        
        # 生成SRT文件测试
        if result and "timestamp" in result[0]:
            timestamps = result[0]["timestamp"]
            print(f"词级时间戳数量: {len(timestamps)}")
            
            # 生成SRT文件
            srt_base_path = "/tmp/test_precision_calibrated"
            srt_paths = transcriber.generate_bilingual_srt(
                text,
                srt_base_path,
                transcription_result=result,
                target_languages=['en'],
                audio_duration=audio_duration
            )
            
            print(f"✓ SRT文件生成完成: {list(srt_paths.keys())}")
            
            # 分析生成的SRT文件
            for lang, srt_path in srt_paths.items():
                if os.path.exists(srt_path):
                    analyze_precision_srt(srt_path, audio_duration)
                else:
                    print(f"✗ {lang} SRT文件不存在: {srt_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_precision_srt(srt_path, expected_duration):
    """分析精确校准后的SRT文件"""
    try:
        print(f"\n=== 精确校准SRT分析: {srt_path} ===")
        
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取时间戳
        timestamp_pattern = r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})'
        timestamps = re.findall(timestamp_pattern, content)
        
        if not timestamps:
            print("✗ 无法提取时间戳")
            return
        
        print(f"字幕块数量: {len(timestamps)}")
        
        # 转换时间戳为秒
        def time_to_seconds(time_str):
            h, m, s_ms = time_str.split(':')
            s, ms = s_ms.split(',')
            return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000
        
        # 分析最后一个时间戳
        last_start = time_to_seconds(timestamps[-1][0])
        last_end = time_to_seconds(timestamps[-1][1])
        
        print(f"最后一个字幕: {timestamps[-1][0]} --> {timestamps[-1][1]}")
        print(f"最后字幕开始时间: {last_start:.3f}秒 ({int(last_start//60)}:{int(last_start%60):02d})")
        print(f"最后字幕结束时间: {last_end:.3f}秒 ({int(last_end//60)}:{int(last_end%60):02d})")
        print(f"预期音频时长: {expected_duration:.3f}秒 ({int(expected_duration//60)}:{int(expected_duration%60):02d})")
        
        # 计算误差
        end_time_error = abs(last_end - expected_duration)
        print(f"结束时间误差: {end_time_error:.3f}秒")
        
        # 分析最后一句的时长
        last_duration = last_end - last_start
        print(f"最后一句时长: {last_duration:.3f}秒")
        
        # 提取最后一句的文本内容
        lines = content.strip().split('\n\n')
        if lines:
            last_block = lines[-1]
            last_text_lines = last_block.split('\n')[2:]  # 跳过序号和时间戳
            last_text = '\n'.join(last_text_lines)
            print(f"最后一句文本: '{last_text}'")
        
        # 判断校准效果
        print(f"\n=== 校准效果评估 ===")
        if end_time_error < 0.1:
            print("✅ 结束时间完美校准！")
        elif end_time_error < 1.0:
            print("✅ 结束时间校准良好")
        elif end_time_error < 2.0:
            print("⚠️ 结束时间校准可接受")
        else:
            print("❌ 结束时间校准需要改进")
        
        # 检查最后一句的合理性
        if 1.0 <= last_duration <= 8.0:
            print("✅ 最后一句时长合理")
        else:
            print(f"⚠️ 最后一句时长异常: {last_duration:.3f}秒")
        
        # 与目标时间对比
        target_start = 754.0  # 12:34 = 754秒
        target_end = 756.0    # 12:36 = 756秒
        
        start_diff = abs(last_start - target_start)
        end_diff = abs(last_end - target_end)
        
        print(f"\n=== 与目标时间对比 ===")
        print(f"目标时间: 12:34:00 - 12:36:00 ({target_start:.0f}s - {target_end:.0f}s)")
        print(f"实际时间: {int(last_start//60)}:{int(last_start%60):02d}:{int(last_start%60):02d} - {int(last_end//60)}:{int(last_end%60):02d}:{int(last_end%60):02d}")
        print(f"开始时间偏差: {start_diff:.3f}秒")
        print(f"结束时间偏差: {end_diff:.3f}秒")
        
        if start_diff <= 1.0 and end_diff <= 1.0:
            print("🎉 时间戳精度达到±1秒要求！")
        elif start_diff <= 2.0 and end_diff <= 2.0:
            print("✅ 时间戳精度良好")
        else:
            print("⚠️ 时间戳精度需要进一步优化")
        
        return {
            'last_start': last_start,
            'last_end': last_end,
            'end_error': end_time_error,
            'duration': last_duration,
            'start_diff': start_diff,
            'end_diff': end_diff
        }
        
    except Exception as e:
        print(f"✗ 分析SRT文件失败: {str(e)}")
        return None

def compare_with_previous_version():
    """对比校准前后的效果"""
    print(f"\n=== 校准前后效果对比 ===")
    
    # 之前的结果
    print("校准前:")
    print("- 最后字幕时间: 12:38:302 --> 12:42:048")
    print("- 目标时间: 12:34:000 --> 12:36:000")
    print("- 开始时间偏差: 4.302秒")
    print("- 结束时间偏差: 6.048秒")
    
    # 检查是否有新的结果文件
    calibrated_srt = "/tmp/test_precision_calibrated_CN.srt"
    if os.path.exists(calibrated_srt):
        print("\n校准后:")
        result = analyze_precision_srt(calibrated_srt, 762.048)
        if result:
            improvement_start = 4.302 - result['start_diff']
            improvement_end = 6.048 - result['end_diff']
            print(f"\n改进效果:")
            print(f"- 开始时间改进: {improvement_start:.3f}秒")
            print(f"- 结束时间改进: {improvement_end:.3f}秒")

if __name__ == "__main__":
    success = test_precision_calibration()
    if success:
        compare_with_previous_version()
    else:
        print("测试失败，无法进行对比")
