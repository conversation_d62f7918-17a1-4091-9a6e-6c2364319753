#!/usr/bin/env python3
"""
测试时间戳修复效果的脚本
"""

import os
import sys
import json
import requests
import time

def test_api_with_fixed_timestamps():
    """测试修复后的API时间戳准确性"""
    
    # 测试文件
    test_file = "test.mp3"
    if not os.path.exists(test_file):
        print(f"✗ 测试文件不存在: {test_file}")
        return False
    
    # 上传测试文件到临时位置（模拟实际使用场景）
    print("=== 测试时间戳修复效果 ===")
    
    # 方法1: 直接调用修复后的类
    print("\n1. 直接测试修复后的FunASRTranscriber类:")
    test_direct_transcriber()
    
    # 方法2: 测试API接口（如果服务正在运行）
    print("\n2. 测试API接口:")
    test_api_endpoint()

def test_direct_transcriber():
    """直接测试修复后的转录器"""
    try:
        # 导入修复后的类
        sys.path.append('/data/funasr')
        from funasr_api_zm import FunASRTranscriber
        
        # 创建转录器实例
        transcriber = FunASRTranscriber("iic/SenseVoiceSmall", "cuda:1")
        
        # 转录测试文件
        audio_path = "test.mp3"
        print(f"转录文件: {audio_path}")
        
        text, result, audio_duration = transcriber.transcribe(audio_path)
        print(f"✓ 转录完成")
        print(f"音频时长: {audio_duration:.3f}秒")
        print(f"文本长度: {len(text)}")
        
        # 生成SRT文件测试
        if result and "timestamp" in result[0]:
            timestamps = result[0]["timestamp"]
            print(f"时间戳数量: {len(timestamps)}")
            
            # 测试时间戳转换
            if len(timestamps) > 0:
                last_frame = timestamps[-1][1]
                
                # 使用旧的转换系数
                old_conversion = last_frame * 0.01
                print(f"旧转换系数结果: {old_conversion:.3f}秒")
                
                # 使用新的动态转换系数
                new_conversion_ratio = audio_duration / last_frame
                new_conversion = last_frame * new_conversion_ratio
                print(f"新转换系数: {new_conversion_ratio:.6f}")
                print(f"新转换系数结果: {new_conversion:.3f}秒")
                
                # 计算误差
                old_error = abs(old_conversion - audio_duration)
                new_error = abs(new_conversion - audio_duration)
                
                print(f"旧方法误差: {old_error:.3f}秒 ({old_error/audio_duration*100:.1f}%)")
                print(f"新方法误差: {new_error:.3f}秒 ({new_error/audio_duration*100:.1f}%)")
                
                # 生成SRT文件
                srt_base_path = "/tmp/test_fixed_timestamps"
                srt_paths = transcriber.generate_bilingual_srt(
                    text,
                    srt_base_path,
                    transcription_result=result,
                    target_languages=['en'],
                    audio_duration=audio_duration
                )
                
                print(f"✓ SRT文件生成完成: {list(srt_paths.keys())}")
                
                # 检查生成的SRT文件
                for lang, srt_path in srt_paths.items():
                    if os.path.exists(srt_path):
                        print(f"✓ {lang} SRT文件存在: {srt_path}")
                        
                        # 读取并显示最后几行
                        with open(srt_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            if len(lines) >= 4:
                                print(f"最后一个字幕块:")
                                for line in lines[-4:]:
                                    print(f"  {line.strip()}")
                    else:
                        print(f"✗ {lang} SRT文件不存在: {srt_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 直接测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """测试API接口"""
    try:
        # 检查API是否运行
        api_url = "http://localhost:10097/api/sound/asr_turbo"
        
        # 这里需要实际的音频URL，暂时跳过
        print("API接口测试需要实际的音频URL，暂时跳过")
        return True
        
    except Exception as e:
        print(f"✗ API测试失败: {str(e)}")
        return False

def analyze_srt_file(srt_path):
    """分析SRT文件的时间戳"""
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取时间戳
        import re
        timestamp_pattern = r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})'
        timestamps = re.findall(timestamp_pattern, content)
        
        if timestamps:
            print(f"SRT文件分析: {srt_path}")
            print(f"字幕块数量: {len(timestamps)}")
            print(f"第一个时间戳: {timestamps[0][0]} --> {timestamps[0][1]}")
            print(f"最后一个时间戳: {timestamps[-1][0]} --> {timestamps[-1][1]}")
            
            # 转换最后一个时间戳为秒
            def time_to_seconds(time_str):
                h, m, s_ms = time_str.split(':')
                s, ms = s_ms.split(',')
                return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000
            
            last_end_time = time_to_seconds(timestamps[-1][1])
            print(f"SRT文件总时长: {last_end_time:.3f}秒")
            
            return last_end_time
        else:
            print(f"✗ 无法从SRT文件中提取时间戳: {srt_path}")
            return 0
            
    except Exception as e:
        print(f"✗ 分析SRT文件失败: {str(e)}")
        return 0

if __name__ == "__main__":
    test_api_with_fixed_timestamps()
